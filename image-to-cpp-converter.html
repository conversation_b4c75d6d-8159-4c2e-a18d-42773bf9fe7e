<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image to C++ Code Converter - Arduino Bitmap Generator</title>
    <meta name="description" content="Free online Image to C++ Code Converter for Arduino. Convert PNG, JPG, BMP, GIF to Arduino-ready code for OLED displays like SSD1306. Professional bitmap generator tool.">
    <meta name="keywords" content="image2cpp, Arduino bitmap generator, image to Arduino code converter, OLED image converter, SSD1306 bitmap generator, convert PNG to Arduino code">
    <style>
        /* SKR Electronics Lab Theme Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', 'Inter', Arial, sans-serif;
            background-color: #ffffff;
            color: #333333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px 0;
            border-bottom: 2px solid #e0e0e0;
        }

        .header h1 {
            color: #2196f3;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .header p {
            color: #666666;
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        .tool-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .card {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card h3 {
            color: #2196f3;
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 2px dashed #2196f3;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .upload-area:hover {
            background-color: #f5f5f5;
            border-color: #1976d2;
        }

        .upload-area.dragover {
            background-color: #e3f2fd;
            border-color: #1976d2;
        }

        .btn {
            background-color: #2196f3;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: background-color 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            background-color: #1976d2;
        }

        .btn-secondary {
            background-color: #f5f5f5;
            color: #333333;
            border: 1px solid #e0e0e0;
        }

        .btn-secondary:hover {
            background-color: #eeeeee;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #333333;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .preview-container {
            grid-column: 1 / -1;
            min-height: 300px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fafafa;
        }

        .code-output {
            background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .code-block {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            overflow-x: auto;
            white-space: pre-wrap;
            margin: 15px 0;
        }

        /* Additional styling for form elements */
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            outline: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #2196f3;
            border-radius: 50%;
            cursor: pointer;
        }

        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #2196f3;
            border-radius: 50%;
            cursor: pointer;
            border: none;
        }

        input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            accent-color: #2196f3;
        }

        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 32px;
        }

        .tutorial-step {
            padding: 15px;
            border-left: 3px solid #2196f3;
            background-color: #f8f9fa;
            border-radius: 0 6px 6px 0;
        }

        .preview-controls {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }

        .preview-controls label {
            display: flex;
            align-items: center;
            margin: 0;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .tool-container {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 15px;
            }

            .preview-controls {
                flex-direction: column;
                align-items: flex-start;
            }

            .preview-controls > * {
                margin-bottom: 5px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .header h1 {
                font-size: 1.8rem;
            }

            .card {
                padding: 15px;
            }

            .upload-area {
                padding: 30px 15px;
            }

            .code-block {
                font-size: 0.8rem;
                padding: 15px;
            }
        }

        /* Hidden elements */
        .hidden {
            display: none;
        }

        /* Loading spinner */
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #2196f3;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header Section -->
        <header class="header">
            <h1>Image to C++ Code Converter</h1>
            <p>Professional Arduino bitmap generator for OLED displays. Convert images to Arduino-ready code instantly.</p>
        </header>

        <!-- Main Tool Container -->
        <div class="tool-container">
            <!-- Left Column: Upload and Settings -->
            <div class="left-column">
                <!-- Image Upload Card -->
                <div class="card">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        Upload Image
                    </h3>
                    <div class="upload-area" id="uploadArea">
                        <svg width="48" height="48" viewBox="0 0 24 24" fill="#2196f3" style="margin-bottom: 10px;">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                        <p>Drop your image here or click to browse</p>
                        <p style="font-size: 0.9rem; color: #666; margin-top: 5px;">Supports PNG, JPG, GIF, BMP</p>
                    </div>
                    <input type="file" id="fileInput" accept="image/*" class="hidden">
                    
                    <div class="form-group">
                        <label for="pasteArray">Or paste existing byte array:</label>
                        <textarea id="pasteArray" class="form-control" rows="3" placeholder="Paste your byte array here to preview..."></textarea>
                    </div>
                </div>

                <!-- Settings Cards -->
                <div class="settings-grid">
                    <!-- Canvas Settings -->
                    <div class="card">
                        <h3>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19,3H5C3.89,3 3,3.89 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5C21,3.89 20.1,3 19,3M19,5V19H5V5H19Z"/>
                            </svg>
                            Canvas
                        </h3>
                        <div class="form-group">
                            <label for="canvasWidth">Width (px):</label>
                            <input type="number" id="canvasWidth" class="form-control" value="128" min="1" max="1024">
                        </div>
                        <div class="form-group">
                            <label for="canvasHeight">Height (px):</label>
                            <input type="number" id="canvasHeight" class="form-control" value="64" min="1" max="1024">
                        </div>
                        <div class="form-group">
                            <label for="fitMode">Fit Mode:</label>
                            <select id="fitMode" class="form-control">
                                <option value="fit">Fit</option>
                                <option value="fill">Fill</option>
                                <option value="stretch">Stretch</option>
                                <option value="center">Center</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="horizontalAlign">Horizontal Align:</label>
                            <select id="horizontalAlign" class="form-control">
                                <option value="center">Center</option>
                                <option value="left">Left</option>
                                <option value="right">Right</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="verticalAlign">Vertical Align:</label>
                            <select id="verticalAlign" class="form-control">
                                <option value="middle">Middle</option>
                                <option value="top">Top</option>
                                <option value="bottom">Bottom</option>
                            </select>
                        </div>
                    </div>

                    <!-- Colors & Threshold Settings -->
                    <div class="card">
                        <h3>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M17.5,12A1.5,1.5 0 0,1 16,10.5A1.5,1.5 0 0,1 17.5,9A1.5,1.5 0 0,1 19,10.5A1.5,1.5 0 0,1 17.5,12M14.5,8A1.5,1.5 0 0,1 13,6.5A1.5,1.5 0 0,1 14.5,5A1.5,1.5 0 0,1 16,6.5A1.5,1.5 0 0,1 14.5,8M9.5,8A1.5,1.5 0 0,1 8,6.5A1.5,1.5 0 0,1 9.5,5A1.5,1.5 0 0,1 11,6.5A1.5,1.5 0 0,1 9.5,8M6.5,12A1.5,1.5 0 0,1 5,10.5A1.5,1.5 0 0,1 6.5,9A1.5,1.5 0 0,1 8,10.5A1.5,1.5 0 0,1 6.5,12M12,3A9,9 0 0,0 3,12A9,9 0 0,0 12,21A9,9 0 0,0 21,12A9,9 0 0,0 12,3Z"/>
                            </svg>
                            Colors & Threshold
                        </h3>
                        <div class="form-group">
                            <label for="brightnessThreshold">Brightness Threshold: <span id="thresholdValue">128</span></label>
                            <input type="range" id="brightnessThreshold" class="form-control" min="0" max="255" value="128">
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="invertColors"> Invert Colors
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="backgroundColor">Background:</label>
                            <select id="backgroundColor" class="form-control">
                                <option value="white">White</option>
                                <option value="black">Black</option>
                                <option value="transparent">Transparent</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableDithering"> Enable Dithering
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="ditheringIntensity">Dithering Intensity: <span id="ditheringValue">50</span>%</label>
                            <input type="range" id="ditheringIntensity" class="form-control" min="0" max="100" value="50" disabled>
                        </div>
                    </div>

                    <!-- Output Settings -->
                    <div class="card">
                        <h3>
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            Output
                        </h3>
                        <div class="form-group">
                            <label for="outputFormat">Format:</label>
                            <select id="outputFormat" class="form-control">
                                <option value="arduino">Arduino .cpp</option>
                                <option value="header">Header .h</option>
                                <option value="json">JSON</option>
                                <option value="csv">CSV</option>
                                <option value="base64">Base64</option>
                                <option value="hex">Hex Array</option>
                                <option value="pbm">PBM</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="variableName">Variable Name:</label>
                            <input type="text" id="variableName" class="form-control" value="bitmap" placeholder="bitmap">
                        </div>
                        <div class="form-group">
                            <label for="dataType">Data Type:</label>
                            <select id="dataType" class="form-control">
                                <option value="uint8_t">uint8_t</option>
                                <option value="uint16_t">uint16_t</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="drawMode">Draw Mode:</label>
                            <select id="drawMode" class="form-control">
                                <option value="horizontal">Horizontal 1bpp</option>
                                <option value="vertical">Vertical</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="animationReady"> Animation-ready array
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Preview and Output -->
            <div class="right-column">
                <!-- Preview Card -->
                <div class="card">
                    <h3>
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                        </svg>
                        Preview
                    </h3>
                    <div class="preview-controls" style="margin-bottom: 15px;">
                        <button class="btn btn-secondary" id="zoomInBtn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5,14H20.5L15.5,9V14M9.5,14H14.5V9L9.5,14M15.5,10H20.5L15.5,15V10M9.5,10H14.5V15L9.5,10Z"/>
                            </svg>
                            Zoom In
                        </button>
                        <button class="btn btn-secondary" id="zoomOutBtn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5,14H20.5L15.5,9V14M9.5,14H14.5V9L9.5,14M15.5,10H20.5L15.5,15V10M9.5,10H14.5V15L9.5,10Z"/>
                            </svg>
                            Zoom Out
                        </button>
                        <label style="margin-left: 15px;">
                            <input type="checkbox" id="showGridlines"> Show Gridlines
                        </label>
                        <span id="previewInfo" style="margin-left: 15px; color: #666; font-size: 0.9rem;"></span>
                    </div>
                    <div class="preview-container" id="previewContainer">
                        <p style="color: #666;">Upload an image to see preview</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Code Output Section -->
        <div class="card">
            <h3>
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M8,3A2,2 0 0,0 6,5V9A2,2 0 0,1 4,11H3V13H4A2,2 0 0,1 6,15V19A2,2 0 0,0 8,21H10V19H8V14A2,2 0 0,0 6,12A2,2 0 0,0 8,10V5H10V3M16,3A2,2 0 0,1 18,5V9A2,2 0 0,0 20,11H21V13H20A2,2 0 0,0 18,15V19A2,2 0 0,1 16,21H14V19H16V14A2,2 0 0,1 18,12A2,2 0 0,1 16,10V5H14V3H16Z"/>
                </svg>
                Generated Code
            </h3>
            <div class="form-group">
                <button class="btn" id="copyBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/>
                    </svg>
                    Copy Code
                </button>
                <button class="btn btn-secondary" id="downloadBtn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z"/>
                    </svg>
                    Download .cpp
                </button>
            </div>
            <div class="code-block" id="codeOutput">
                // Upload an image to generate Arduino code
            </div>
        </div>

        <!-- Tutorial Section -->
        <div class="card" style="margin-top: 40px;">
            <h2 style="color: #2196f3; font-size: 2rem; margin-bottom: 20px;">How to Use the Image to C++ Code Converter</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                <div class="tutorial-step">
                    <h4 style="color: #2196f3; margin-bottom: 10px;">1. Upload Your Image</h4>
                    <p>Drag and drop your image file (PNG, JPG, GIF, BMP) into the upload area, or click to browse and select a file. You can also paste an existing byte array to preview or re-convert.</p>
                </div>
                <div class="tutorial-step">
                    <h4 style="color: #2196f3; margin-bottom: 10px;">2. Adjust Canvas Settings</h4>
                    <p>Set your desired canvas width and height in pixels. Choose the fit mode (Fill, Fit, Stretch, Center) and alignment options to control how your image is positioned.</p>
                </div>
                <div class="tutorial-step">
                    <h4 style="color: #2196f3; margin-bottom: 10px;">3. Configure Colors & Threshold</h4>
                    <p>Adjust the brightness threshold slider to control which pixels become black or white. Enable invert colors if needed, choose background color, and optionally enable dithering for better image quality.</p>
                </div>
                <div class="tutorial-step">
                    <h4 style="color: #2196f3; margin-bottom: 10px;">4. Choose Output Format</h4>
                    <p>Select your desired output format (Arduino .cpp, Header .h, JSON, CSV, Base64, Hex Array, or PBM). Customize the variable name, data type, and draw mode as needed.</p>
                </div>
                <div class="tutorial-step">
                    <h4 style="color: #2196f3; margin-bottom: 10px;">5. Review Preview</h4>
                    <p>Check the live bitmap preview to verify your image looks correct. Use zoom controls and gridlines toggle to inspect the pixel-level details of your converted image.</p>
                </div>
                <div class="tutorial-step">
                    <h4 style="color: #2196f3; margin-bottom: 10px;">6. Copy or Download Code</h4>
                    <p>Copy the generated Arduino-ready code to your clipboard or download it as a .cpp or .h file. Paste the code into Arduino IDE and upload it to your microcontroller board.</p>
                </div>
            </div>
        </div>

        <!-- SEO Content Section -->
        <div class="card" style="margin-top: 30px;">
            <h2 style="color: #2196f3; font-size: 1.8rem; margin-bottom: 20px;">Professional Arduino Bitmap Generator & Image2cpp Alternative</h2>
            <div style="line-height: 1.8; color: #444;">
                <p>Our <strong>Image to C++ Code Converter</strong> is a free, professional web-based tool designed to help makers, hobbyists, and engineers convert images into Arduino-ready code effortlessly. Whether you're looking for an <strong>image2cpp alternative</strong> or an advanced <strong>Arduino bitmap generator</strong>, this tool supports PNG, JPG, BMP, and GIF formats, and outputs clean .cpp and .h files ready to upload to your microcontroller projects.</p>

                <p>Optimized for <strong>SSD1306</strong>, SH1106, and other <strong>OLED displays</strong>, our <strong>OLED image converter</strong> provides professional-grade features including customizable canvas dimensions, multiple fit modes, brightness threshold control, color inversion, and optional dithering for enhanced image quality. The tool generates <strong>Arduino .cpp file</strong> output with complete setup and loop code, including all necessary library includes for immediate use.</p>

                <p>This <strong>SSD1306 bitmap generator</strong> is fully client-side, ensuring your images never leave your device while providing lightning-fast processing. The responsive design works perfectly on desktop, tablet, and mobile devices, making it the ideal solution when you need to <strong>convert PNG to Arduino code</strong> on the go. Advanced features include multiple output formats (JSON, CSV, Base64, Hex Array, PBM), customizable variable names, different data types (uint8_t, uint16_t), and animation-ready array generation.</p>

                <p>Perfect for Arduino projects involving OLED displays, LED matrices, e-paper displays, and any application requiring bitmap data. The tool generates optimized PROGMEM arrays that save valuable RAM on your microcontroller. Try our professional <strong>Arduino bitmap generator</strong> online now - no downloads needed, completely free, and fully compatible with Arduino IDE, PlatformIO, and other development environments.</p>
            </div>
        </div>
    </div>

    <script>
        // Initialize the Image to C++ Converter
        class ImageToCppConverter {
            constructor() {
                this.canvas = null;
                this.ctx = null;
                this.imageData = null;
                this.originalImage = null;
                this.zoomLevel = 1;
                this.init();
            }

            init() {
                this.setupEventListeners();
                this.createCanvas();
            }

            setupEventListeners() {
                const uploadArea = document.getElementById('uploadArea');
                const fileInput = document.getElementById('fileInput');

                // File upload events
                uploadArea.addEventListener('click', () => fileInput.click());
                uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                fileInput.addEventListener('change', this.handleFileSelect.bind(this));

                // Settings change events
                document.getElementById('canvasWidth').addEventListener('input', this.updatePreview.bind(this));
                document.getElementById('canvasHeight').addEventListener('input', this.updatePreview.bind(this));
                document.getElementById('fitMode').addEventListener('change', this.updatePreview.bind(this));
                document.getElementById('horizontalAlign').addEventListener('change', this.updatePreview.bind(this));
                document.getElementById('verticalAlign').addEventListener('change', this.updatePreview.bind(this));
                document.getElementById('brightnessThreshold').addEventListener('input', this.updateThreshold.bind(this));
                document.getElementById('invertColors').addEventListener('change', this.updatePreview.bind(this));
                document.getElementById('backgroundColor').addEventListener('change', this.updatePreview.bind(this));
                document.getElementById('enableDithering').addEventListener('change', this.toggleDithering.bind(this));
                document.getElementById('ditheringIntensity').addEventListener('input', this.updateDithering.bind(this));
                document.getElementById('outputFormat').addEventListener('change', this.generateCode.bind(this));
                document.getElementById('variableName').addEventListener('input', this.generateCode.bind(this));
                document.getElementById('dataType').addEventListener('change', this.generateCode.bind(this));
                document.getElementById('drawMode').addEventListener('change', this.generateCode.bind(this));
                document.getElementById('animationReady').addEventListener('change', this.generateCode.bind(this));

                // Preview controls
                document.getElementById('zoomInBtn').addEventListener('click', this.zoomIn.bind(this));
                document.getElementById('zoomOutBtn').addEventListener('click', this.zoomOut.bind(this));
                document.getElementById('showGridlines').addEventListener('change', this.toggleGridlines.bind(this));

                // Button events
                document.getElementById('copyBtn').addEventListener('click', this.copyCode.bind(this));
                document.getElementById('downloadBtn').addEventListener('click', this.downloadCode.bind(this));

                // Paste array functionality
                document.getElementById('pasteArray').addEventListener('input', this.handlePasteArray.bind(this));
            }

            createCanvas() {
                this.canvas = document.createElement('canvas');
                this.ctx = this.canvas.getContext('2d');
            }

            handleDragOver(e) {
                e.preventDefault();
                e.currentTarget.classList.add('dragover');
            }

            handleDrop(e) {
                e.preventDefault();
                e.currentTarget.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.processFile(files[0]);
                }
            }

            // Add drag leave event to remove dragover class
            handleDragLeave(e) {
                e.preventDefault();
                e.currentTarget.classList.remove('dragover');
            }

            handleFileSelect(e) {
                const file = e.target.files[0];
                if (file) {
                    this.processFile(file);
                }
            }

            processFile(file) {
                if (!file.type.startsWith('image/')) {
                    alert('Please select a valid image file.');
                    return;
                }

                const reader = new FileReader();
                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        this.processImage(img);
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            }

            processImage(img) {
                this.originalImage = img;
                const width = parseInt(document.getElementById('canvasWidth').value);
                const height = parseInt(document.getElementById('canvasHeight').value);

                this.canvas.width = width;
                this.canvas.height = height;

                // Set background color
                const bgColor = document.getElementById('backgroundColor').value;
                this.ctx.fillStyle = bgColor === 'white' ? '#ffffff' : bgColor === 'black' ? '#000000' : 'transparent';
                if (bgColor !== 'transparent') {
                    this.ctx.fillRect(0, 0, width, height);
                }

                // Draw image based on fit mode and alignment
                this.drawImageWithFitMode(img, width, height);

                // Update preview and generate code
                this.updatePreview();
                this.generateCode();
            }

            updateThreshold() {
                const threshold = document.getElementById('brightnessThreshold').value;
                document.getElementById('thresholdValue').textContent = threshold;
                if (this.originalImage) {
                    this.processImage(this.originalImage);
                }
            }

            toggleDithering() {
                const enabled = document.getElementById('enableDithering').checked;
                document.getElementById('ditheringIntensity').disabled = !enabled;
                if (this.originalImage) {
                    this.processImage(this.originalImage);
                }
            }

            updateDithering() {
                const intensity = document.getElementById('ditheringIntensity').value;
                document.getElementById('ditheringValue').textContent = intensity;
                if (this.originalImage) {
                    this.processImage(this.originalImage);
                }
            }

            zoomIn() {
                this.zoomLevel = Math.min(this.zoomLevel * 2, 16);
                this.updatePreview();
            }

            zoomOut() {
                this.zoomLevel = Math.max(this.zoomLevel / 2, 0.25);
                this.updatePreview();
            }

            toggleGridlines() {
                this.updatePreview();
            }

            handlePasteArray() {
                const arrayText = document.getElementById('pasteArray').value.trim();
                if (arrayText) {
                    try {
                        // Parse the byte array and create a preview
                        this.parseByteArray(arrayText);
                    } catch (error) {
                        console.error('Error parsing byte array:', error);
                    }
                }
            }

            drawImageWithFitMode(img, canvasWidth, canvasHeight) {
                const fitMode = document.getElementById('fitMode').value;
                const hAlign = document.getElementById('horizontalAlign').value;
                const vAlign = document.getElementById('verticalAlign').value;

                switch (fitMode) {
                    case 'stretch':
                        this.ctx.drawImage(img, 0, 0, canvasWidth, canvasHeight);
                        break;
                    case 'fit':
                        this.drawImageFit(img, canvasWidth, canvasHeight, hAlign, vAlign);
                        break;
                    case 'fill':
                        this.drawImageFill(img, canvasWidth, canvasHeight, hAlign, vAlign);
                        break;
                    case 'center':
                        this.drawImageCenter(img, canvasWidth, canvasHeight, hAlign, vAlign);
                        break;
                }
            }

            drawImageFit(img, canvasWidth, canvasHeight, hAlign, vAlign) {
                const scale = Math.min(canvasWidth / img.width, canvasHeight / img.height);
                const width = img.width * scale;
                const height = img.height * scale;

                let x, y;
                switch (hAlign) {
                    case 'left': x = 0; break;
                    case 'right': x = canvasWidth - width; break;
                    default: x = (canvasWidth - width) / 2; break;
                }
                switch (vAlign) {
                    case 'top': y = 0; break;
                    case 'bottom': y = canvasHeight - height; break;
                    default: y = (canvasHeight - height) / 2; break;
                }

                this.ctx.drawImage(img, x, y, width, height);
            }

            drawImageFill(img, canvasWidth, canvasHeight, hAlign, vAlign) {
                const scale = Math.max(canvasWidth / img.width, canvasHeight / img.height);
                const width = img.width * scale;
                const height = img.height * scale;

                let x, y;
                switch (hAlign) {
                    case 'left': x = 0; break;
                    case 'right': x = canvasWidth - width; break;
                    default: x = (canvasWidth - width) / 2; break;
                }
                switch (vAlign) {
                    case 'top': y = 0; break;
                    case 'bottom': y = canvasHeight - height; break;
                    default: y = (canvasHeight - height) / 2; break;
                }

                this.ctx.drawImage(img, x, y, width, height);
            }

            drawImageCenter(img, canvasWidth, canvasHeight, hAlign, vAlign) {
                let x, y;
                switch (hAlign) {
                    case 'left': x = 0; break;
                    case 'right': x = canvasWidth - img.width; break;
                    default: x = (canvasWidth - img.width) / 2; break;
                }
                switch (vAlign) {
                    case 'top': y = 0; break;
                    case 'bottom': y = canvasHeight - img.height; break;
                    default: y = (canvasHeight - img.height) / 2; break;
                }

                this.ctx.drawImage(img, x, y);
            }

            updatePreview() {
                if (!this.canvas) return;

                const previewContainer = document.getElementById('previewContainer');
                const previewInfo = document.getElementById('previewInfo');
                previewContainer.innerHTML = '';

                // Create preview canvas with zoom
                const previewCanvas = document.createElement('canvas');
                const previewCtx = previewCanvas.getContext('2d');

                const scaledWidth = this.canvas.width * this.zoomLevel;
                const scaledHeight = this.canvas.height * this.zoomLevel;

                previewCanvas.width = scaledWidth;
                previewCanvas.height = scaledHeight;

                // Disable image smoothing for pixel-perfect scaling
                previewCtx.imageSmoothingEnabled = false;
                previewCtx.drawImage(this.canvas, 0, 0, scaledWidth, scaledHeight);

                // Draw gridlines if enabled
                if (document.getElementById('showGridlines').checked && this.zoomLevel >= 4) {
                    this.drawGridlines(previewCtx, scaledWidth, scaledHeight);
                }

                // Style preview canvas
                previewCanvas.style.maxWidth = '100%';
                previewCanvas.style.maxHeight = '400px';
                previewCanvas.style.border = '1px solid #e0e0e0';
                previewCanvas.style.imageRendering = 'pixelated';

                previewContainer.appendChild(previewCanvas);

                // Update info
                previewInfo.textContent = `${this.canvas.width}×${this.canvas.height} | Zoom: ${Math.round(this.zoomLevel * 100)}%`;
            }

            drawGridlines(ctx, width, height) {
                ctx.strokeStyle = '#2196f3';
                ctx.lineWidth = 0.5;
                ctx.globalAlpha = 0.3;

                const pixelSize = this.zoomLevel;

                // Vertical lines
                for (let x = 0; x <= width; x += pixelSize) {
                    ctx.beginPath();
                    ctx.moveTo(x, 0);
                    ctx.lineTo(x, height);
                    ctx.stroke();
                }

                // Horizontal lines
                for (let y = 0; y <= height; y += pixelSize) {
                    ctx.beginPath();
                    ctx.moveTo(0, y);
                    ctx.lineTo(width, y);
                    ctx.stroke();
                }

                ctx.globalAlpha = 1;
            }

            generateCode() {
                if (!this.canvas) return;

                const width = this.canvas.width;
                const height = this.canvas.height;
                const imageData = this.ctx.getImageData(0, 0, width, height);

                // Convert to bitmap array
                const bitmap = this.convertToBitmap(imageData, width, height);

                // Generate code based on selected format
                const format = document.getElementById('outputFormat').value;
                let code;

                switch (format) {
                    case 'arduino':
                        code = this.generateArduinoCode(bitmap, width, height);
                        break;
                    case 'header':
                        code = this.generateHeaderCode(bitmap, width, height);
                        break;
                    case 'json':
                        code = this.generateJsonCode(bitmap, width, height);
                        break;
                    case 'csv':
                        code = this.generateCsvCode(bitmap, width, height);
                        break;
                    case 'base64':
                        code = this.generateBase64Code(bitmap, width, height);
                        break;
                    case 'hex':
                        code = this.generateHexCode(bitmap, width, height);
                        break;
                    case 'pbm':
                        code = this.generatePbmCode(bitmap, width, height);
                        break;
                    default:
                        code = this.generateArduinoCode(bitmap, width, height);
                }

                document.getElementById('codeOutput').textContent = code;
            }

            convertToBitmap(imageData, width, height) {
                const bitmap = [];
                const data = imageData.data;
                const threshold = parseInt(document.getElementById('brightnessThreshold').value);
                const invert = document.getElementById('invertColors').checked;
                const enableDithering = document.getElementById('enableDithering').checked;
                const ditheringIntensity = parseInt(document.getElementById('ditheringIntensity').value) / 100;
                const drawMode = document.getElementById('drawMode').value;

                // Create a copy of image data for dithering
                const processedData = new Uint8ClampedArray(data);

                if (enableDithering) {
                    this.applyFloydSteinbergDithering(processedData, width, height, threshold, ditheringIntensity);
                }

                if (drawMode === 'vertical') {
                    return this.convertToBitmapVertical(processedData, width, height, threshold, invert);
                } else {
                    return this.convertToBitmapHorizontal(processedData, width, height, threshold, invert);
                }
            }

            convertToBitmapHorizontal(data, width, height, threshold, invert) {
                const bitmap = [];

                for (let y = 0; y < height; y += 8) {
                    for (let x = 0; x < width; x++) {
                        let byte = 0;
                        for (let bit = 0; bit < 8; bit++) {
                            if (y + bit < height) {
                                const index = ((y + bit) * width + x) * 4;
                                const r = data[index];
                                const g = data[index + 1];
                                const b = data[index + 2];
                                const brightness = (r + g + b) / 3;

                                let pixelOn = brightness < threshold;
                                if (invert) pixelOn = !pixelOn;

                                if (pixelOn) {
                                    byte |= (1 << bit);
                                }
                            }
                        }
                        bitmap.push(byte);
                    }
                }

                return bitmap;
            }

            convertToBitmapVertical(data, width, height, threshold, invert) {
                const bitmap = [];

                for (let x = 0; x < width; x += 8) {
                    for (let y = 0; y < height; y++) {
                        let byte = 0;
                        for (let bit = 0; bit < 8; bit++) {
                            if (x + bit < width) {
                                const index = (y * width + (x + bit)) * 4;
                                const r = data[index];
                                const g = data[index + 1];
                                const b = data[index + 2];
                                const brightness = (r + g + b) / 3;

                                let pixelOn = brightness < threshold;
                                if (invert) pixelOn = !pixelOn;

                                if (pixelOn) {
                                    byte |= (1 << bit);
                                }
                            }
                        }
                        bitmap.push(byte);
                    }
                }

                return bitmap;
            }

            applyFloydSteinbergDithering(data, width, height, threshold, intensity) {
                for (let y = 0; y < height; y++) {
                    for (let x = 0; x < width; x++) {
                        const index = (y * width + x) * 4;
                        const oldPixel = (data[index] + data[index + 1] + data[index + 2]) / 3;
                        const newPixel = oldPixel < threshold ? 0 : 255;
                        const error = (oldPixel - newPixel) * intensity;

                        data[index] = data[index + 1] = data[index + 2] = newPixel;

                        // Distribute error to neighboring pixels
                        if (x + 1 < width) {
                            const rightIndex = (y * width + x + 1) * 4;
                            data[rightIndex] = Math.max(0, Math.min(255, data[rightIndex] + error * 7/16));
                            data[rightIndex + 1] = Math.max(0, Math.min(255, data[rightIndex + 1] + error * 7/16));
                            data[rightIndex + 2] = Math.max(0, Math.min(255, data[rightIndex + 2] + error * 7/16));
                        }

                        if (y + 1 < height) {
                            if (x - 1 >= 0) {
                                const bottomLeftIndex = ((y + 1) * width + x - 1) * 4;
                                data[bottomLeftIndex] = Math.max(0, Math.min(255, data[bottomLeftIndex] + error * 3/16));
                                data[bottomLeftIndex + 1] = Math.max(0, Math.min(255, data[bottomLeftIndex + 1] + error * 3/16));
                                data[bottomLeftIndex + 2] = Math.max(0, Math.min(255, data[bottomLeftIndex + 2] + error * 3/16));
                            }

                            const bottomIndex = ((y + 1) * width + x) * 4;
                            data[bottomIndex] = Math.max(0, Math.min(255, data[bottomIndex] + error * 5/16));
                            data[bottomIndex + 1] = Math.max(0, Math.min(255, data[bottomIndex + 1] + error * 5/16));
                            data[bottomIndex + 2] = Math.max(0, Math.min(255, data[bottomIndex + 2] + error * 5/16));

                            if (x + 1 < width) {
                                const bottomRightIndex = ((y + 1) * width + x + 1) * 4;
                                data[bottomRightIndex] = Math.max(0, Math.min(255, data[bottomRightIndex] + error * 1/16));
                                data[bottomRightIndex + 1] = Math.max(0, Math.min(255, data[bottomRightIndex + 1] + error * 1/16));
                                data[bottomRightIndex + 2] = Math.max(0, Math.min(255, data[bottomRightIndex + 2] + error * 1/16));
                            }
                        }
                    }
                }
            }

            generateArduinoCode(bitmap, width, height) {
                const arrayName = document.getElementById('variableName').value || 'bitmap';
                const dataType = document.getElementById('dataType').value;
                const animationReady = document.getElementById('animationReady').checked;

                let code = `// Generated by SKR Electronics Lab Image to C++ Converter\n`;
                code += `// Image dimensions: ${width}x${height}\n`;
                code += `// Generated on: ${new Date().toLocaleString()}\n\n`;
                code += `#include <Wire.h>\n`;
                code += `#include <Adafruit_GFX.h>\n`;
                code += `#include <Adafruit_SSD1306.h>\n\n`;
                code += `// OLED display configuration\n`;
                code += `#define SCREEN_WIDTH ${width}\n`;
                code += `#define SCREEN_HEIGHT ${height}\n`;
                code += `#define OLED_RESET -1\n`;
                code += `// For I2C: Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);\n`;
                code += `// For SPI: Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &SPI, DC_PIN, RESET_PIN, CS_PIN);\n`;
                code += `Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);\n\n`;

                if (animationReady) {
                    code += `// Animation frame data\n`;
                    code += `const ${dataType} PROGMEM ${arrayName}_frame[] = {\n`;
                } else {
                    code += `// Bitmap array stored in PROGMEM\n`;
                    code += `const ${dataType} PROGMEM ${arrayName}[] = {\n`;
                }

                // Add bitmap data
                for (let i = 0; i < bitmap.length; i++) {
                    if (i % 16 === 0) code += '  ';
                    code += `0x${bitmap[i].toString(16).padStart(2, '0').toUpperCase()}`;
                    if (i < bitmap.length - 1) code += ', ';
                    if ((i + 1) % 16 === 0 || i === bitmap.length - 1) code += '\n';
                }

                code += `};\n\n`;
                code += `void setup() {\n`;
                code += `  Serial.begin(9600);\n`;
                code += `  \n`;
                code += `  // Initialize OLED display\n`;
                code += `  if(!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {\n`;
                code += `    Serial.println(F("SSD1306 allocation failed"));\n`;
                code += `    for(;;);\n`;
                code += `  }\n`;
                code += `  \n`;
                code += `  // Clear display and show bitmap\n`;
                code += `  display.clearDisplay();\n`;
                if (animationReady) {
                    code += `  display.drawBitmap(0, 0, ${arrayName}_frame, ${width}, ${height}, SSD1306_WHITE);\n`;
                } else {
                    code += `  display.drawBitmap(0, 0, ${arrayName}, ${width}, ${height}, SSD1306_WHITE);\n`;
                }
                code += `  display.display();\n`;
                code += `}\n\n`;
                code += `void loop() {\n`;
                code += `  // Your main code here\n`;
                if (animationReady) {
                    code += `  // Add animation logic here\n`;
                }
                code += `}\n`;

                return code;
            }

            generateHeaderCode(bitmap, width, height) {
                const arrayName = document.getElementById('variableName').value || 'bitmap';
                const dataType = document.getElementById('dataType').value;

                let code = `// Generated by SKR Electronics Lab Image to C++ Converter\n`;
                code += `// Header file for ${arrayName}\n`;
                code += `// Image dimensions: ${width}x${height}\n`;
                code += `// Generated on: ${new Date().toLocaleString()}\n\n`;
                code += `#ifndef ${arrayName.toUpperCase()}_H\n`;
                code += `#define ${arrayName.toUpperCase()}_H\n\n`;
                code += `#include <Arduino.h>\n\n`;
                code += `// Image dimensions\n`;
                code += `#define ${arrayName.toUpperCase()}_WIDTH ${width}\n`;
                code += `#define ${arrayName.toUpperCase()}_HEIGHT ${height}\n\n`;
                code += `// Bitmap data\n`;
                code += `extern const ${dataType} PROGMEM ${arrayName}[];\n\n`;
                code += `#endif // ${arrayName.toUpperCase()}_H\n`;

                return code;
            }

            generateJsonCode(bitmap, width, height) {
                const arrayName = document.getElementById('variableName').value || 'bitmap';

                const jsonData = {
                    name: arrayName,
                    width: width,
                    height: height,
                    format: 'monochrome',
                    generated: new Date().toISOString(),
                    data: bitmap
                };

                return JSON.stringify(jsonData, null, 2);
            }

            generateCsvCode(bitmap, width, height) {
                const arrayName = document.getElementById('variableName').value || 'bitmap';

                let csv = `# ${arrayName} - ${width}x${height}\n`;
                csv += `# Generated by SKR Electronics Lab Image to C++ Converter\n`;
                csv += `# Generated on: ${new Date().toLocaleString()}\n`;
                csv += `width,height,data\n`;
                csv += `${width},${height},"${bitmap.join(',')}"\n`;

                return csv;
            }

            generateBase64Code(bitmap, width, height) {
                const arrayName = document.getElementById('variableName').value || 'bitmap';
                const uint8Array = new Uint8Array(bitmap);
                const base64 = btoa(String.fromCharCode.apply(null, uint8Array));

                let code = `// ${arrayName} - ${width}x${height}\n`;
                code += `// Generated by SKR Electronics Lab Image to C++ Converter\n`;
                code += `// Base64 encoded bitmap data\n\n`;
                code += `const char* ${arrayName}_base64 = "${base64}";\n`;
                code += `const int ${arrayName}_width = ${width};\n`;
                code += `const int ${arrayName}_height = ${height};\n`;

                return code;
            }

            generateHexCode(bitmap, width, height) {
                const arrayName = document.getElementById('variableName').value || 'bitmap';
                const dataType = document.getElementById('dataType').value;

                let code = `// ${arrayName} - ${width}x${height}\n`;
                code += `// Generated by SKR Electronics Lab Image to C++ Converter\n`;
                code += `// Hex array format\n\n`;
                code += `const ${dataType} ${arrayName}[${bitmap.length}] = {\n`;

                for (let i = 0; i < bitmap.length; i++) {
                    if (i % 16 === 0) code += '  ';
                    code += `0x${bitmap[i].toString(16).padStart(2, '0').toUpperCase()}`;
                    if (i < bitmap.length - 1) code += ', ';
                    if ((i + 1) % 16 === 0 || i === bitmap.length - 1) code += '\n';
                }

                code += `};\n`;

                return code;
            }

            generatePbmCode(bitmap, width, height) {
                let pbm = `P4\n`;
                pbm += `# Generated by SKR Electronics Lab Image to C++ Converter\n`;
                pbm += `${width} ${height}\n`;

                // Convert bitmap to PBM format
                const pbmData = [];
                for (let i = 0; i < bitmap.length; i++) {
                    pbmData.push(String.fromCharCode(bitmap[i]));
                }

                pbm += pbmData.join('');

                return pbm;
            }

            parseByteArray(arrayText) {
                // Parse various byte array formats
                let bytes = [];

                // Remove common prefixes and clean up
                let cleanText = arrayText.replace(/const\s+\w+\s+\w+\[\]\s*=\s*\{|\};?/g, '')
                                       .replace(/0x/g, '')
                                       .replace(/[{}]/g, '')
                                       .replace(/\s+/g, ' ')
                                       .trim();

                // Split by comma and parse
                const parts = cleanText.split(',');
                for (let part of parts) {
                    part = part.trim();
                    if (part) {
                        const value = parseInt(part, 16) || parseInt(part, 10);
                        if (!isNaN(value) && value >= 0 && value <= 255) {
                            bytes.push(value);
                        }
                    }
                }

                if (bytes.length > 0) {
                    // Try to determine dimensions (assume square-ish for now)
                    const totalBits = bytes.length * 8;
                    const side = Math.sqrt(totalBits);
                    const width = Math.ceil(side);
                    const height = Math.ceil(totalBits / width);

                    // Create a canvas and draw the bitmap
                    this.createBitmapFromArray(bytes, width, height);
                }
            }

            createBitmapFromArray(bytes, width, height) {
                this.canvas = document.createElement('canvas');
                this.ctx = this.canvas.getContext('2d');
                this.canvas.width = width;
                this.canvas.height = height;

                const imageData = this.ctx.createImageData(width, height);
                const data = imageData.data;

                // Convert bytes back to pixels
                let byteIndex = 0;
                for (let y = 0; y < height; y += 8) {
                    for (let x = 0; x < width; x++) {
                        if (byteIndex < bytes.length) {
                            const byte = bytes[byteIndex++];
                            for (let bit = 0; bit < 8; bit++) {
                                if (y + bit < height) {
                                    const pixelIndex = ((y + bit) * width + x) * 4;
                                    const isOn = (byte & (1 << bit)) !== 0;
                                    const color = isOn ? 0 : 255;
                                    data[pixelIndex] = color;     // R
                                    data[pixelIndex + 1] = color; // G
                                    data[pixelIndex + 2] = color; // B
                                    data[pixelIndex + 3] = 255;   // A
                                }
                            }
                        }
                    }
                }

                this.ctx.putImageData(imageData, 0, 0);
                this.updatePreview();
                this.generateCode();
            }

            copyCode() {
                const code = document.getElementById('codeOutput').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    const btn = document.getElementById('copyBtn');
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/></svg> Copied!';
                    setTimeout(() => {
                        btn.innerHTML = originalText;
                    }, 2000);
                }).catch(err => {
                    console.error('Failed to copy code:', err);
                    // Fallback for older browsers
                    const textArea = document.createElement('textarea');
                    textArea.value = code;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                });
            }

            downloadCode() {
                const code = document.getElementById('codeOutput').textContent;
                const format = document.getElementById('outputFormat').value;
                const arrayName = document.getElementById('variableName').value || 'bitmap';

                let filename, mimeType;
                switch (format) {
                    case 'arduino':
                        filename = `${arrayName}.cpp`;
                        mimeType = 'text/x-c++src';
                        break;
                    case 'header':
                        filename = `${arrayName}.h`;
                        mimeType = 'text/x-chdr';
                        break;
                    case 'json':
                        filename = `${arrayName}.json`;
                        mimeType = 'application/json';
                        break;
                    case 'csv':
                        filename = `${arrayName}.csv`;
                        mimeType = 'text/csv';
                        break;
                    case 'pbm':
                        filename = `${arrayName}.pbm`;
                        mimeType = 'image/x-portable-bitmap';
                        break;
                    default:
                        filename = `${arrayName}.txt`;
                        mimeType = 'text/plain';
                }

                const blob = new Blob([code], { type: mimeType });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        }

        // Initialize the converter when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ImageToCppConverter();
        });
    </script>
</body>
</html>
