# Image to C++ Code Converter by SKR Electronics Lab

A professional, fully client-side web-based Image to C++ Code Converter tool designed for Arduino and microcontroller projects. Convert images to Arduino-ready code for OLED displays like SSD1306, SH1106, and more.

## Features

### 🖼️ Image Processing
- **Multiple Format Support**: PNG, JPG, GIF, BMP
- **Drag & Drop Upload**: Easy file upload with visual feedback
- **Paste Byte Array**: Import existing byte arrays for re-conversion
- **Live Preview**: Real-time image preview with zoom and gridlines

### ⚙️ Advanced Settings
- **Canvas Control**: Customizable width/height (1-1024px)
- **Fit Modes**: Fill, Fit, Stretch, Center with alignment options
- **Color Processing**: Brightness threshold, color inversion, background selection
- **Dithering**: Optional Floyd-Steinberg dithering with intensity control

### 📤 Multiple Output Formats
- **Arduino .cpp**: Complete sketch with setup/loop code
- **Header .h**: Header files with proper guards
- **JSON**: Structured data format
- **CSV**: Comma-separated values
- **Base64**: Encoded bitmap data
- **Hex Array**: Raw hexadecimal arrays
- **PBM**: Portable Bitmap format

### 🎯 Professional Features
- **PROGMEM Optimization**: Memory-efficient Arduino code
- **Animation Ready**: Support for animation frame arrays
- **Multiple Data Types**: uint8_t, uint16_t support
- **Draw Modes**: Horizontal 1bpp and Vertical modes
- **Copy & Download**: One-click code copying and file downloads

## Usage

### For Elementor (WordPress)
1. Copy the entire content of `image-to-cpp-converter.html`
2. In Elementor, add an HTML widget
3. Paste the code into the HTML widget
4. Save and publish

### Standalone Usage
1. Open `image-to-cpp-converter.html` in any modern web browser
2. Upload your image or paste an existing byte array
3. Adjust settings as needed
4. Copy the generated code or download the file
5. Use in your Arduino IDE or development environment

## Technical Specifications

- **Fully Client-Side**: No server required, works offline
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Cross-Browser Compatible**: Modern browsers (Chrome, Firefox, Safari, Edge)
- **Google Adsense Compliant**: Clean, professional code
- **SEO Optimized**: Includes comprehensive SEO content

## Arduino Integration

The generated code includes:
- Complete library includes (Wire.h, Adafruit_GFX.h, Adafruit_SSD1306.h)
- Display initialization code
- PROGMEM bitmap arrays for memory efficiency
- Ready-to-use setup() and loop() functions
- I2C and SPI configuration comments

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## File Structure

```
image-to-cpp-converter.html    # Complete standalone tool
README.md                      # This documentation
```

## Keywords & SEO

This tool serves as a professional alternative to image2cpp and other Arduino bitmap generators, specifically optimized for:
- Arduino bitmap generation
- OLED image conversion
- SSD1306 bitmap creation
- PNG to Arduino code conversion
- Microcontroller display projects

## License

Created by SKR Electronics Lab. Free for personal and commercial use.

## Support

For support, feature requests, or bug reports, please contact SKR Electronics Lab.

---

**SKR Electronics Lab** - Professional Arduino and Electronics Solutions
